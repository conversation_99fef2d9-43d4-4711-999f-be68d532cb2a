#!/bin/bash

pkill -f crontab_summary

cd /data/hw-data-go/rt_data_analyse_iaa/
nohup ./crontab_summary tag=summary_cost_data &
nohup ./crontab_summary tag=adjust_match &
nohup ./crontab_summary tag=max_hour_report-max_ltv_report-max_user_report &

nohup ./crontab_summary tag=summary_ad_roi &
nohup ./crontab_summary tag=summary_operation &
nohup ./crontab_summary tag=summary_ad_position &
nohup ./crontab_summary tag=summary_ad_user &

nohup ./crontab_summary tag=summary_ad_roi zone=EUR &
nohup ./crontab_summary tag=summary_operation zone=EUR &
nohup ./crontab_summary tag=summary_ad_position zone=EUR &
nohup ./crontab_summary tag=summary_ad_user zone=EUR &

nohup ./crontab_summary tag=summary_ad_roi zone=US &
nohup ./crontab_summary tag=summary_operation zone=US &
nohup ./crontab_summary tag=summary_ad_position zone=US &
nohup ./crontab_summary tag=summary_ad_user zone=US &


