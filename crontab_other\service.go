package crontab_other

import (
	"910.com/plus2.git/contract"
	"iaa_data/crontab_other/task/check_cost"
	"iaa_data/crontab_other/task/check_login"
	"iaa_data/crontab_other/task/event_upload"
)

type Service struct{}

func NewService() *Service {
	return &Service{}
}

func (s *Service) InitHandler(c contract.Crontab) error {
	c.AddFunc("0 */30 * * * *", event_upload.InitFunc, "event_upload")
	c.AddFunc("0 */60 * * * *", check_cost.InitFunc, "check_cost")
	c.AddFunc("0 */3 * * * *", check_login.InitFunc, "check_login")
	return nil
}
