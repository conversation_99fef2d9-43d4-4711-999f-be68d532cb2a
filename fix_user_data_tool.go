package main

import (
	"910.com/plus2.git/plus"
	"910.com/plus2.git/provider"
	"iaa_data/crontab_other/task/check_login/fix_user_data"
	"iaa_data/utils/args_iaa"
	"log"
	"time"
)

func main() {
	configFile := "config/config_hw.yaml"
	_, err := plus.LoadServerConfigDefault(configFile)
	if err != nil {
		log.Printf("Failed to load config: %+v", err)
	}
	plus.AppConfig.ConsoleLogFile = "./log_common/crontab_other/demo.log"
	provider.Register() //注册服务

	start := args_iaa.GetArgs("start")
	end := args_iaa.GetArgs("end")

	startT, err := time.Parse("2006-01-02", start)
	if err != nil {
		log.Printf("Failed to parse start time: %+v", err)
		return
	}
	endT, err := time.Parse("2006-01-02", end)
	if err != nil {
		log.Printf("Failed to parse end time: %+v", err)
		return
	}

	mType := args_iaa.GetArgs("type")

	startTime := startT.Format("2006-01-02") + " 00:00:00"
	endTime := endT.Format("2006-01-02") + " 23:59:59"
	log.Println(startTime, endTime)

	if mType == "" {
		fix_user_data.FixTimeErr(startTime, endTime)
	} else if mType == "login" {
		fix_user_data.FixNoLogin(startTime, endTime)
	}
}
