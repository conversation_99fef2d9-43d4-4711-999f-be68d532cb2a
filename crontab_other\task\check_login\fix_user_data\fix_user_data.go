package fix_user_data

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/utils/sql_helper"
	"log"
	"time"
)

func FixNullList() {
	sql := "SELECT r.*  FROM hw_sdk_user_reg r LEFT JOIN hw_sdk_user_login l ON r.core_account = l.core_account WHERE l.core_account IS NULL;"

	list, err := plusQ.Db("data").List(sql)
	if err != nil {
		log.Println("==============kai", err.Error())
		return
	}
	for _, v := range list {
		t, err := time.Parse("2006-01-02 15:04:05 +0800 CST", fmt.Sprint(v["time_bj"]))
		if err != nil {
			log.Println("时间转换出错", err.Error())
			return
		}
		insertUserLoginLog(v, t.Format(time.DateTime))
		insertUserNewLoginPackageLog(v, t.Format(time.DateTime))
		insertUserNewLoginCpGameLog(v, t.Format(time.DateTime))
		insertUserActivateLog(v, t.Format(time.DateTime))
	}
}

func FixTimeErr(startTime, endTime string) {

	checkRegNullLogin(startTime, endTime)
	checkNewLoginNullReg(startTime, endTime)
	checkAdNullLogin(startTime, endTime)

	tableList := []string{}
	tableList = append(tableList, "hw_sdk_user_reg")
	tableList = append(tableList, "hw_sdk_user_login_package")
	tableList = append(tableList, "hw_sdk_user_login_cp_game")
	tableList = append(tableList, "hw_sdk_active_log")
	for _, tableName := range tableList {
		CheckTableData(startTime, endTime, tableName)
	}
}

func FixNoLogin(startTime, endTime string) {
	checkRegNullLogin(startTime, endTime)
}

func checkRegNullLogin(startTime, endTime string) string {
	sql := "select r.* from hw_sdk_user_reg r left join hw_sdk_user_login l on " +
		" r.core_account = l.core_account where " +
		"   r.time_bj between '" + startTime + "' and '" + endTime + "'" +
		"   and l.core_account is null "

	list, _ := plusQ.Db("data").List(sql)

	if len(list) > 0 {
		for _, item := range list {
			t, err := time.Parse("2006-01-02 15:04:05 +0800 CST", fmt.Sprint(item["time_bj"]))
			if err != nil {
				log.Println("时间转换出错", err.Error())
				continue
			}
			insertUserLoginLog(item, t.Format(time.DateTime))
			insertUserNewLoginPackageLog(item, t.Format(time.DateTime))
			insertUserNewLoginCpGameLog(item, t.Format(time.DateTime))
			insertUserActivateLog(item, t.Format(time.DateTime))
		}
	}
	return ""
}

func checkNewLoginNullReg(startTime, endTime string) string {
	sql := "select r.* from hw_sdk_user_login_package r left join hw_sdk_user_reg l on " +
		" r.core_account = l.core_account where " +
		"   r.time_bj between '" + startTime + "' and '" + endTime + "'" +
		"   and l.core_account is null "

	list, _ := plusQ.Db("data").List(sql)

	if len(list) > 0 {
		for _, item := range list {
			t, err := time.Parse("2006-01-02 15:04:05 +0800 CST", fmt.Sprint(item["time_bj"]))
			if err != nil {
				log.Println("时间转换出错", err.Error())
				continue
			}
			insertRegisterLog(item, t.Format(time.DateTime))
		}
	}
	return ""
}

func checkAdNullLogin(startTime, endTime string) string {
	sql := "select r.cp_game_id , r.core_account from hw_sdk_ad_revenue_log r left join hw_sdk_user_login_package l on " +
		" r.core_account = l.core_account where " +
		"   r.time_bj between '" + startTime + "' and '" + endTime + "'" +
		"   and l.core_account is null group by core_account,package_id"

	list, _ := plusQ.Db("data").List(sql)

	if len(list) > 0 {
		for _, item := range list {
			t, err := time.Parse("2006-01-02 15:04:05 +0800 CST", fmt.Sprint(item["time_bj"]))
			if err != nil {
				log.Println("时间转换出错", err.Error())
				continue
			}
			item["login_type"] = "guest"
			insertUserLoginLog(item, t.Format(time.DateTime))
			insertUserNewLoginPackageLog(item, t.Format(time.DateTime))
			insertUserNewLoginCpGameLog(item, t.Format(time.DateTime))
			insertUserActivateLog(item, t.Format(time.DateTime))
		}
	}
	return ""
}

func CheckTableData(startTime, endTime, tableName string) {
	sql := "select A.* , B.id as mid,B.time_bj as oTime " +
		" from hw_sdk_ad_revenue_log as A join " + tableName + " as B using(game_id," +
		" PACKAGE_ID, CORE_ACCOUNT) where " +
		" A.time_bj  between '" + startTime + "' and '" + endTime + "' " +
		" and B.time_bj between '" + startTime + "' and '" + endTime + "' " +
		" group by A.core_account  " +
		" having " +
		" A.time_bj<B.time_bj"
	if tableName == "hw_sdk_active_log" {
		sql = "select A.* , B.id as mid,B.time_bj as oTime " +
			" from hw_sdk_ad_revenue_log as A join " + tableName + " as B using(" +
			" package_id, device_code) where " +
			" A.time_bj  between '" + startTime + "' and '" + endTime + "' " +
			" and B.time_bj between '" + startTime + "' and '" + endTime + "' " +
			" group by A.core_account  " +
			" having " +
			" A.time_bj<B.time_bj"
	}

	list, _ := plusQ.Db("data").List(sql)
	if len(list) > 0 {
		for _, item := range list {
			t, err := time.Parse("2006-01-02 15:04:05 +0800 CST", fmt.Sprint(item["time_bj"]))
			if err != nil {
				log.Println("时间转换出错", err.Error())
				continue
			}
			mid := fmt.Sprint(item["mid"])
			sqlUpdate := "update " + tableName + " set time_j='" + t.Format(time.DateTime) + "' where mid=" + mid

			_, errUpdate := plusQ.Db("data").Update(sqlUpdate)
			if errUpdate != nil {
				log.Println("更新失败", errUpdate.Error(), sqlUpdate)
			} else {
				log.Println("更新成功")
			}
		}
	}
}

func insertUserLoginLog(item map[string]interface{}, timeBj string) error {
	info := make(map[string]interface{})
	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["login_account"] = item["core_account"]
	info["core_account"] = item["core_account"]
	info["time_local"] = timeBj
	info["time_bj"] = timeBj
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_server"] = timeBj
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["device_type"] = item["device_type"]
	info["os"] = item["os"]
	info["os_version"] = item["os_version"]
	info["sdk_version"] = item["sdk_version"]
	info["game_version"] = item["game_version"]
	info["network_type"] = item["network_type"]
	info["mobile_type"] = item["mobile_type"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	info["login_type"] = item["login_type"]
	sql_helper.InsertOrUpdate("data", "hw_sdk_user_login", info)
	return nil
}

func insertUserNewLoginPackageLog(item map[string]interface{}, timeBj string) {
	info := make(map[string]interface{})
	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["login_account"] = item["core_account"]
	info["core_account"] = item["core_account"]
	info["time_local"] = timeBj
	info["time_bj"] = timeBj
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_server"] = timeBj
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["device_type"] = item["device_type"]
	info["os"] = item["os"]
	info["os_version"] = item["os_version"]
	info["sdk_version"] = item["sdk_version"]
	info["game_version"] = item["game_version"]
	info["network_type"] = item["network_type"]
	info["mobile_type"] = item["mobile_type"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	info["login_type"] = item["login_type"]
	sql_helper.InsertOrUpdate("data", "hw_sdk_user_login_package", info)
}

func insertUserNewLoginCpGameLog(item map[string]interface{}, timeBj string) {

	info := make(map[string]interface{})
	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["login_account"] = item["core_account"]
	info["core_account"] = item["core_account"]
	info["time_local"] = timeBj
	info["time_bj"] = timeBj
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_server"] = timeBj
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["device_type"] = item["device_type"]
	info["os"] = item["os"]
	info["os_version"] = item["os_version"]
	info["sdk_version"] = item["sdk_version"]
	info["game_version"] = item["game_version"]
	info["network_type"] = item["network_type"]
	info["mobile_type"] = item["mobile_type"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	info["login_type"] = item["login_type"]
	sql_helper.InsertOrUpdate("data", "hw_sdk_user_login_cp_game", info)
}

func insertUserActivateLog(item map[string]interface{}, timeBj string) {
	t, err := time.Parse(time.DateTime, timeBj)
	if err != nil {
		return
	}
	tDayStr := t.Format(time.DateOnly)
	tDay, _ := time.Parse(time.DateOnly, tDayStr)

	tDayLocalStr := tDayStr
	tDayLocal, _ := time.Parse(time.DateOnly, tDayLocalStr)

	info := make(map[string]interface{})
	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["login_account"] = item["core_account"]
	info["core_account"] = item["core_account"]
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_bj"] = timeBj
	info["tday"] = tDay.Format(time.DateOnly)
	info["local_tday"] = tDayLocal.Format(time.DateOnly)
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["os"] = item["os"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	sql_helper.InsertOnly("data", "hw_sdk_user_active", info)
}

func insertRegisterLog(item map[string]interface{}, timeBj string) {
	info := make(map[string]interface{})

	info["cp_game_id"] = item["cp_game_id"]
	info["game_id"] = item["game_id"]
	info["package_id"] = item["package_id"]
	info["core_account"] = item["core_account"]
	info["aid_or_idfv"] = item["aid_or_idfv"]
	info["gid_or_idfa"] = item["gid_or_idfa"]
	info["time_bj"] = timeBj
	info["time_local"] = timeBj
	info["time_server"] = timeBj
	info["device_code"] = item["device_code"]
	info["useragent"] = item["useragent"]
	info["device_type"] = item["device_type"]
	info["os"] = item["os"]
	info["os_version"] = item["os_version"]
	info["sdk_version"] = item["sdk_version"]
	info["game_version"] = item["game_version"]
	info["network_type"] = item["network_type"]
	info["mobile_type"] = item["mobile_type"]
	info["ip"] = item["ip"]
	info["country"] = item["country"]
	info["mac"] = item["mac"]
	info["device_language"] = item["device_language"]
	info["adjust_id"] = item["adjust_id"]
	info["login_type"] = item["login_type"]
	sql_helper.InsertOrUpdate("data", "hw_sdk_user_reg", info)
}
