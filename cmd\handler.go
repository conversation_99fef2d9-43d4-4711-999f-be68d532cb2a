package cmd

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"strings"
)

type Grab interface {
	Run(params map[string]string) error
}

// Handler 处理所有命令
type Handler struct {
	commands map[string]Grab
}

// NewHandler 创建一个新的命令处理器
func NewHandler() *Handler {
	h := &Handler{
		commands: make(map[string]Grab),
	}

	// 注册所有命令
	h.RegisterCommands()

	return h
}

// RegisterCommands 注册所有可用的命令
func (h *Handler) RegisterCommands() {
	h.commands["facebook_grab"] = &FacebookGrab{}
	h.commands["facebook_grab_hour"] = &FacebookGrabHour{}
	h.commands["google_grab"] = &GoogleGrab{}
	h.commands["tiktok_grab"] = &TiktokGrab{}
	h.commands["tiktok_grab_hour"] = &TiktokGrabHour{}
	h.commands["applovin_grab"] = &ApplovinGrab{}
	h.commands["applovin_grab_hour"] = &ApplovinGrabHour{}
	h.commands["facebook_md5"] = &FacebookMd5{}
	h.commands["mintegral_grab"] = &MintegralGrab{}
}

// Execute 执行指定的命令
func (h *Handler) Execute(operation string, args []string) error {
	cmd, exists := h.commands[operation]
	if !exists {
		return fmt.Errorf("未知的操作类型: %s", operation)
	}
	// 手动解析参数
	params := make(map[string]string)

	for i := 0; i < len(args); i++ {
		arg := args[i]
		if strings.HasPrefix(arg, "-") {
			paramName := strings.TrimPrefix(arg, "-")
			if i+1 < len(args) && !strings.HasPrefix(args[i+1], "-") {
				params[paramName] = args[i+1]
				i++ // 跳过下一个参数
			}
		}
	}

	err := cmd.Run(params)

	if err != nil {
		plusQ.Logger().Info(operation, fmt.Sprintf("%v", err))
	}

	return err
}

// GetAvailableCommands 获取所有可用的命令列表
func (h *Handler) GetAvailableCommands() []string {
	commands := make([]string, 0, len(h.commands))
	for cmd := range h.commands {
		commands = append(commands, cmd)
	}
	return commands
}
