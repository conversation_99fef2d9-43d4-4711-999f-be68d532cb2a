package check_login

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/utils/day_check_helper"
	"iaa_data/utils/root_iaa"
	"log"
	"time"
)

func InitFunc(args ...string) {

	if day_check_helper.CheckDay9("check_user_err_info2") {
		return
	}

	nowTime := time.Now().Add(-24 * time.Hour)
	startTime := nowTime.Format("2006-01-02") + " 00:00:00"
	endTime := nowTime.Format("2006-01-02") + " 23:59:59"

	CheckAllData(startTime, endTime, "")
	CheckAllData(startTime, endTime, "11")

}

func CheckAllData(startTime, endTime, cpGameId string) {
	t, _ := time.Parse(time.DateTime, startTime)

	tableList := []string{}
	tableList = append(tableList, "hw_sdk_user_reg")
	tableList = append(tableList, "hw_sdk_user_login_package")
	tableList = append(tableList, "hw_sdk_user_login_cp_game")
	tableList = append(tableList, "hw_sdk_active_log")

	val := ""
	msg1 := checkRegNullLogin(startTime, endTime, cpGameId)
	if msg1 != "" {
		val = msg1
	}
	msg2 := checkNewLoginNullReg(startTime, endTime, cpGameId)
	if msg2 != "" {
		val = val + msg2
	}
	msg3 := checkAdNullLogin(startTime, endTime, cpGameId)
	if msg3 != "" {
		val = val + msg3
	}

	for _, tableName := range tableList {
		msg := CheckTableData(startTime, endTime, tableName, cpGameId)
		if msg != "" {
			val = val + msg
		}
	}
	if len(val) > 0 {
		if cpGameId == "" {
			val = "# 其他游戏 异常数据 " + t.Format(time.DateOnly) + "\n" + val
		} else {
			val = "# M001 异常数据 " + t.Format(time.DateOnly) + "\n" + val
		}
		root_iaa.RobotInstance().Send(val)
	}

	log.Println(val)
}

func checkRegNullLogin(startTime, endTime, cpGameId string) string {
	sql := "select r.cp_game_id , r.core_account from hw_sdk_user_reg r left join hw_sdk_user_login l on " +
		" r.core_account = l.core_account where " +
		"   r.time_bj between '" + startTime + "' and '" + endTime + "'" +
		"   and l.core_account is null "
	if cpGameId != "" {
		sql = sql + " and r.cp_game_id = " + cpGameId
	} else {
		sql = sql + " and r.cp_game_id !=11 "
	}

	list, _ := plusQ.Db("data").List(sql)

	if len(list) > 0 {
		return "存在注册无登陆记录：<font color=\"warning\">" + fmt.Sprint(len(list)) + "</font>\n"
	}
	return ""
}

func checkNewLoginNullReg(startTime, endTime, cpGameId string) string {
	sql := "select r.cp_game_id , r.core_account from hw_sdk_user_login_package r left join hw_sdk_user_reg l on " +
		" r.core_account = l.core_account where " +
		"   r.time_bj between '" + startTime + "' and '" + endTime + "'" +
		"   and l.core_account is null "
	if cpGameId != "" {
		sql = sql + " and r.cp_game_id = " + cpGameId
	} else {
		sql = sql + " and r.cp_game_id != 11"
	}
	list, _ := plusQ.Db("data").List(sql)

	if len(list) > 0 {
		return "存在新增无注册记录：<font color=\"warning\">" + fmt.Sprint(len(list)) + "</font>\n"
	}
	return ""
}

func checkAdNullLogin(startTime, endTime, cpGameId string) string {
	sql := "select r.cp_game_id , r.core_account,r.package_id  from hw_sdk_ad_revenue_log r left join hw_sdk_user_login_package l on " +
		" r.core_account = l.core_account where " +
		"   r.time_bj between '" + startTime + "' and '" + endTime + "'"
	if cpGameId != "" {
		sql = sql + " and r.cp_game_id = " + cpGameId
	} else {
		sql = sql + " and r.cp_game_id != 11"
	}
	sql = sql + "   and l.core_account is null group by core_account,package_id"
	list, _ := plusQ.Db("data").List(sql)

	if len(list) > 0 {
		return "存在广告日志无新增记录：<font color=\"warning\">" + fmt.Sprint(len(list)) + "</font>\n"
	}
	return ""
}

func CheckTableData(startTime, endTime, tableName, cpGameId string) string {
	sql := "select core_account , B.TIME_BJ as login_time, A.time_bj as revenue_time" +
		" from hw_sdk_ad_revenue_log as A join " + tableName + " as B using(game_id," +
		" PACKAGE_ID, CORE_ACCOUNT) where " +
		" A.time_bj  between '" + startTime + "' and '" + endTime + "' " +
		" and B.time_bj between '" + startTime + "' and '" + endTime + "' "
	if cpGameId != "" {
		sql = sql + " and A.cp_game_id = " + cpGameId + " and B.cp_game_id = " + cpGameId
	} else {
		sql = sql + " and A.cp_game_id != 11 and B.cp_game_id !=11 "
	}

	sql2 := " group by A.core_account  " +
		" having " +
		" A.time_bj<B.time_bj"
	sql = sql + sql2

	if tableName == "hw_sdk_active_log" {
		sql = "select core_account , B.TIME_BJ as login_time, A.time_bj as revenue_time" +
			" from hw_sdk_ad_revenue_log as A join " + tableName + " as B using(" +
			" package_id, device_code) where " +
			" A.time_bj  between '" + startTime + "' and '" + endTime + "' " +
			" and B.time_bj between '" + startTime + "' and '" + endTime + "' "
		if cpGameId != "" {
			sql = sql + " and A.cp_game_id = " + cpGameId + " and B.cp_game_id = " + cpGameId
		} else {
			sql = sql + " and A.cp_game_id != 11 and B.cp_game_id !=11 "
		}
		sql2 = " group by A.core_account  " +
			" having " +
			" A.time_bj<B.time_bj"
		sql = sql + sql2
	}

	list, _ := plusQ.Db("data").List(sql)
	if len(list) > 0 {
		return tableName + " 入库时间比广告日志延迟：<font color=\"warning\">" + fmt.Sprint(len(list)) + "</font>\n"
	}

	return ""
}
