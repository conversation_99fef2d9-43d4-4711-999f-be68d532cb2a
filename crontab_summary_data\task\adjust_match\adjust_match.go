package adjust_match

import (
	"910.com/plus2.git/plusQ"
	"fmt"
	"iaa_data/model/data/hw_sdk_user_login_package"
	"iaa_data/utils/game_helper"
	"iaa_data/utils/source_helper"
	"log"
	"time"
)

var oldHour = 0
var searchNotAccount = ""

func InitFunc(args ...string) {

	nowTime := time.Now()

	//每五分钟更新前半小时
	startTime1 := nowTime.Add(-30 * time.Minute)
	matchAll(startTime1, nowTime, "")

	if nowTime.Hour() != oldHour {
		oldHour = nowTime.Hour()
		//每小时第五分钟更新今日凌晨到现在的数据
		timeStr := nowTime.Format(time.DateOnly)
		timeStr = timeStr + " 00:00:00"
		startTime2, _ := time.Parse(time.DateTime, timeStr)
		matchAll(startTime2, nowTime, "")
	}

	if nowTime.Hour() == 1 {
		val, err := plusQ.Cache("c0").Get("match_adjust_match")
		if err == nil {
			if fmt.Sprint(val) == nowTime.Format("2006-01-02") {
				return
			}
		}
		plusQ.Cache("c0").Set("match_adjust_match", nowTime.Format("2006-01-02"), 0)
		//每天凌晨1:00更新前两天数据
		startTime3 := nowTime.Add(-48 * time.Hour)
		matchAll(startTime3, nowTime, "")
	}

}

func Replay(start, end time.Time) {
	end = end.Add(24 * time.Hour)
	//matchAll(start, end, "replay")//全部重新匹配
	matchAll(start, end, "") //匹配未匹配的
}

func PlayAdUser(start, end time.Time) {
	end = end.Add(24 * time.Hour)
	matchAdUser(start, end, "")
}

func matchAll(startTime, endTime time.Time, playType string) {
	matchSqlInfo(startTime, endTime, "hw_sdk_user_login_cp_game", playType)
	matchSqlInfo(startTime, endTime, "hw_sdk_user_login_package", playType)
	matchSqlInfo(startTime, endTime, "hw_sdk_user_payment", playType)
	matchSqlInfo(startTime, endTime, "hw_sdk_active_log", playType)
	matchSqlInfo(startTime, endTime, "hw_sdk_user_reg", playType)
	matchSqlInfo(startTime, endTime, "hw_sdk_start_log", playType)

	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_user_login", "time_bj", playType)
	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_user_active", "time_bj", playType)
	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_ad_click_log", "time_bj", playType)
	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_ad_show_log", "time_bj", playType)
	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_ad_revenue_log", "time_bj", playType)
	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_ad_reward_log", "time_bj", playType)
	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_ad_loaded_log", "time_bj", playType)
	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_ad_close_log", "time_bj", playType)
	matchSqlInfoForAccount(startTime, endTime, "hw_sdk_ad_request_log", "time_bj", playType)
}

func matchAdUser(startTime, endTime time.Time, playType string) {
	matchAdUserAdjust(startTime, endTime, "ad_report_user", "`date`", playType)
}

func matchSqlInfoForAccount(startTime, endTime time.Time, tableName, timeTag, playType string) {
	sql := "select * from " + tableName + " where " + timeTag + " between " + "'" + startTime.Format(time.DateTime) +
		"' and '" + endTime.Format(time.DateTime) + "' "

	if playType == "" {
		sql = sql + " and click_id = 0"
	}
	sql = sql + " order by core_account desc"
	log.Println("===matchSqlInfoForAccount==========", sql)

	list, err := plusQ.Db("data").List(sql)

	if err != nil {
		log.Println("查询失败"+tableName, err)
		return
	}

	searchAccountMatch(list, endTime, tableName)
}

func matchSqlInfo(startTime, endTime time.Time, tableName, playType string) {
	sql := "select * from " + tableName + " where time_bj between " + "'" + startTime.Format(time.DateTime) +
		"' and '" + endTime.Format(time.DateTime) + "' "
	list, err := plusQ.Db("data").List(sql)

	if playType == "" {
		sql = sql + " and click_id = 0"
	}
	log.Println("===matchSqlInfoForAccount==========", sql)
	if err != nil {
		log.Println("查询失败", err)
		return
	}

	searchAdjustAndMatch(list, endTime, tableName)
}

func searchAccountMatch(list []map[string]interface{}, nowTime time.Time, tableName string) {
	for _, item := range list {
		coreAccount := fmt.Sprint(item["core_account"])

		if searchNotAccount == coreAccount {
			continue
		}

		conLogin := hw_sdk_user_login_package.CoreAccount.Equal(coreAccount)
		infoLogin, err := hw_sdk_user_login_package.GetBy(conLogin)
		if err != nil {
			searchNotAccount = coreAccount
			log.Println("查询失败:"+coreAccount, err)
			continue
		}
		if len(infoLogin.AdjustId) == 0 || infoLogin.ClickId == 0 {
			continue
		}

		searchNotAccount = ""

		creativeIdName := "creative_id"
		if tableName == "hw_sdk_ad_revenue_log" {
			creativeIdName = "iaa_creative_id"
		}

		sql := "update " + tableName + " set click_id = " + fmt.Sprint(infoLogin.ClickId) +
			",source_id = " + fmt.Sprint(infoLogin.SourceId) +
			",country = '" + infoLogin.Country + "'" +
			",adjust_id = '" + infoLogin.AdjustId + "'," +
			creativeIdName + " = '" + fmt.Sprint(infoLogin.CreativeId) +
			"' where id = " + fmt.Sprint(item["id"])

		_, err = plusQ.Db("data").Update(sql)
		if err != nil {
			log.Println("更新失败", err)
			log.Println("更新失败", sql)
		}
	}
}

func searchAdjustAndMatch(list []map[string]interface{}, nowTime time.Time, tableName string) {

	for _, item := range list {
		aidOrIdfv := fmt.Sprint(item["aid_or_idfv"])
		gidOrIdfa := fmt.Sprint(item["gid_or_idfa"])
		adjustId := fmt.Sprint(item["adjust_id"])
		package_id := fmt.Sprint(item["package_id"])

		searchSql := "select * from hw_device_active_source where package_id =" + package_id + " and  activate_time < " + "'" + nowTime.Format(time.DateTime) + "'"

		if len(adjustId) > 0 && gidOrIdfa != "<nil>" {
			searchSql = searchSql + " and adjust_id=" + "'" + adjustId + "'"
		} else {
			orSql1 := ""
			if len(gidOrIdfa) > 0 && gidOrIdfa != "<nil>" && gidOrIdfa != "00000000-0000-0000-0000-000000000000" {
				orSql1 = " gid_or_idfa=" + "'" + gidOrIdfa + "'"
			}
			orSql2 := ""
			if len(aidOrIdfv) > 0 && aidOrIdfv != "<nil>" {
				orSql2 = " aid_or_idfv=" + "'" + aidOrIdfv + "'"
			}
			if orSql1 == "" && orSql2 == "" {
				continue
			}
			if orSql1 != "" && orSql2 != "" {
				searchSql = searchSql + " and (" + orSql1 + " or " + orSql2 + " ) "
			} else {
				if orSql1 != "" {
					searchSql = searchSql + " and " + orSql1
				} else {
					searchSql = searchSql + " and " + orSql2
				}
			}
		}
		//log.Println("====searchSqlsearchSql=======", searchSql)
		infoAdjust, err := plusQ.Db("data").Get(searchSql)
		if err != nil {
			log.Println("查询失败conAdjust", err)
			continue
		}
		if infoAdjust == nil {
			continue
		}
		if len(fmt.Sprint(infoAdjust["adjust_id"])) == 0 {
			continue
		}

		source := fmt.Sprint(infoAdjust["source"])
		sourceId, err := source_helper.SourceInstant().GetSourceId(source)
		if err != nil {
			continue
		}

		sql := "update " + tableName + " set click_id = " + fmt.Sprint(infoAdjust["id"]) +
			",source_id = " + fmt.Sprint(sourceId) +
			",country = '" + fmt.Sprint(infoAdjust["country_code"]) +
			"',creative_id = '" + fmt.Sprint(infoAdjust["creative_id"]) +
			"',adjust_id = '" + fmt.Sprint(infoAdjust["adjust_id"]) +
			"' where id = " + fmt.Sprint(item["id"])
		_, err = plusQ.Db("data").Update(sql)
		if err != nil {
			log.Println("更新失败", err)
		}
	}
}

func matchAdUserAdjust(startTime, endTime time.Time, tableName, timeTag, playType string) {
	sql2 := "select * from " + tableName + " where " + timeTag + " between " + "'" + startTime.Format(time.DateTime) +
		"' and '" + endTime.Format(time.DateTime) + "' "

	if playType == "" {
		sql2 = sql2 + " and click_id = 0"
	}
	log.Println("===matchSqlInfoForAccount==========", sql2)

	list, err := plusQ.Db("data").List(sql2)

	if err != nil {
		log.Println("查询失败"+tableName, err)
		return
	}

	for _, item := range list {
		idfv := fmt.Sprint(item["idfv"])
		idfa := fmt.Sprint(item["idfa"])
		packageName := fmt.Sprint(item["package_id"])
		_, _, packageId, err := game_helper.GameHelInstant().GetPackageInfo(packageName)

		searchSql := "select * from hw_device_active_source where package_id =" + fmt.Sprint(packageId) + " and  activate_time < " + "'" + endTime.Format(time.DateTime) + "'"

		orSql1 := ""
		if len(idfa) > 0 && idfa != "<nil>" {
			orSql1 = " gid_or_idfa=" + "'" + idfa + "'"
		}
		orSql2 := ""
		if len(idfv) > 0 && idfv != "<nil>" {
			orSql2 = " aid_or_idfv=" + "'" + idfv + "'"
		}
		if orSql1 == "" && orSql2 == "" {
			continue
		}
		if orSql1 != "" && orSql2 != "" {
			searchSql = searchSql + " and (" + orSql1 + " or " + orSql2 + " ) "
		} else {
			if orSql1 != "" {
				searchSql = searchSql + " and " + orSql1
			} else {
				searchSql = searchSql + " and " + orSql2
			}
		}

		//log.Println("====searchSqlsearchSql=======", searchSql)
		infoAdjust, err := plusQ.Db("data").Get(searchSql)
		if err != nil {
			log.Println("查询失败conAdjust", err)
			continue
		}
		if infoAdjust == nil {
			continue
		}
		if len(fmt.Sprint(infoAdjust["adjust_id"])) == 0 {
			continue
		}

		source := fmt.Sprint(infoAdjust["source"])
		sourceId, err := source_helper.SourceInstant().GetSourceId(source)
		if err != nil {
			continue
		}

		sql := "update " + tableName + " set click_id = " + fmt.Sprint(infoAdjust["id"]) +
			",source_id = " + fmt.Sprint(sourceId) +
			",country = '" + fmt.Sprint(infoAdjust["country_code"]) +
			"',creative_id = '" + fmt.Sprint(infoAdjust["creative_id"]) +
			"',adjust_id = '" + fmt.Sprint(infoAdjust["adjust_id"]) +
			"' where id = " + fmt.Sprint(item["id"])
		_, err = plusQ.Db("data").Update(sql)
		if err != nil {
			log.Println("更新失败", err)
		}
	}
}
